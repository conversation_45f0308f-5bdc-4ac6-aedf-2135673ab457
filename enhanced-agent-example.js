/**
 * Enhanced Agent Framework Example
 * Demonstrates the improved multi-agent orchestration capabilities
 */

import { Agent, EventEmitter, MemoryManager, ToolHandler } from './Agent2.js';

// Mock LLM Provider for demonstration
class MockLLMProvider {
  constructor(agentName) {
    this.agentName = agentName;
  }

  async generateContent({ contents, config }) {
    // Simulate different agent responses based on their role
    const userMessage = contents[contents.length - 1]?.parts?.[0]?.text || '';
    
    if (config.systemInstruction?.includes('researcher')) {
      return {
        candidates: [{
          content: {
            parts: [{
              text: `Research findings for "${userMessage}": Based on my analysis, I found relevant information about the topic. Key insights include data trends and market analysis.`
            }]
          }
        }]
      };
    } else if (config.systemInstruction?.includes('coder')) {
      return {
        candidates: [{
          content: {
            parts: [{
              text: `Code implementation for "${userMessage}": Here's a robust solution with error handling and best practices implemented.`
            }]
          }
        }]
      };
    } else if (config.systemInstruction?.includes('orchestrator')) {
      // Handle orchestrator responses with JSON for planning/reasoning
      if (userMessage.includes('Evaluate') || userMessage.includes('options')) {
        return {
          candidates: [{
            content: {
              parts: [{
                text: JSON.stringify({
                  steps: [
                    "Analyzed available agents and their capabilities",
                    "Evaluated task complexity and requirements",
                    "Considered current load and performance metrics"
                  ],
                  decision: "Selected best agent based on expertise match and availability"
                })
              }]
            }
          }]
        };
      } else if (userMessage.includes('Decompose') || userMessage.includes('plan')) {
        return {
          candidates: [{
            content: {
              parts: [{
                text: JSON.stringify({
                  subTasks: [
                    { id: "research", task: "Research the topic thoroughly", role: "researcher", dependencies: [] },
                    { id: "implement", task: "Implement the solution", role: "coder", dependencies: ["research"] }
                  ],
                  sequence: ["research", "implement"]
                })
              }]
            }
          }]
        };
      }
    }

    return {
      candidates: [{
        content: {
          parts: [{
            text: `Response from ${this.agentName}: I've processed your request "${userMessage}" and here's my analysis.`
          }]
        }
      }]
    };
  }
}

// Example tools
const researchTool = {
  name: "web_search",
  schema: {
    function_declaration: {
      name: "web_search",
      description: "Search the web for information",
      parameters: {
        type: "object",
        properties: {
          query: { type: "string", description: "Search query" }
        },
        required: ["query"]
      }
    }
  },
  call: async (params) => {
    return `Search results for "${params.query}": Found 10 relevant articles with key insights.`;
  }
};

const codingTool = {
  name: "code_generator",
  schema: {
    function_declaration: {
      name: "code_generator",
      description: "Generate code based on specifications",
      parameters: {
        type: "object",
        properties: {
          language: { type: "string", description: "Programming language" },
          specification: { type: "string", description: "Code specification" }
        },
        required: ["language", "specification"]
      }
    }
  },
  call: async (params) => {
    return `Generated ${params.language} code for: ${params.specification}`;
  }
};

async function demonstrateEnhancedAgentFramework() {
  console.log('🚀 Enhanced Agent Framework Demonstration\n');

  // Create orchestrator agent
  const orchestrator = new Agent({
    id: 'orchestrator-001',
    name: 'Master Orchestrator',
    description: 'Coordinates and delegates tasks to specialized agents',
    role: 'You are a master orchestrator agent responsible for breaking down complex tasks and delegating them to appropriate specialized agents.',
    goals: ['Task decomposition', 'Agent coordination', 'Workflow optimization'],
    llmProvider: new MockLLMProvider('Orchestrator'),
    isOrchestrator: true,
    maxRetries: 2,
    failureThreshold: 3
  });

  // Create specialized worker agents
  const researcher = new Agent({
    id: 'researcher-001',
    name: 'Research Specialist',
    description: 'Specialized in research, data analysis, and information gathering',
    role: 'You are a research specialist focused on gathering and analyzing information.',
    goals: ['Information gathering', 'Data analysis', 'Research synthesis'],
    llmProvider: new MockLLMProvider('Researcher'),
    tools: { web_search: researchTool }
  });

  const coder = new Agent({
    id: 'coder-001',
    name: 'Code Specialist',
    description: 'Specialized in software development and code implementation',
    role: 'You are a coding specialist focused on implementing robust software solutions.',
    goals: ['Code implementation', 'Software architecture', 'Best practices'],
    llmProvider: new MockLLMProvider('Coder'),
    tools: { code_generator: codingTool }
  });

  // Register agents with orchestrator
  orchestrator.registerAgent(researcher);
  orchestrator.registerAgent(coder);

  // Set up event listeners for monitoring
  orchestrator.on('*', (data) => {
    console.log(`📡 Event: ${data.type || 'unknown'}`, data);
  });

  try {
    console.log('📊 Initial Orchestrator Status:');
    console.log(JSON.stringify(orchestrator.getOrchestratorStatus(), null, 2));

    console.log('\n🎯 Orchestrating Complex Task...');
    const complexGoal = "Create a comprehensive market analysis dashboard with real-time data visualization";

    const result = await orchestrator.orchestrate(complexGoal, {
      allowParallel: true,
      useCoT: true,
      context: { priority: 'high', deadline: '2024-01-15' }
    });

    console.log('\n✅ Orchestration Complete!');
    console.log('Result:', JSON.stringify(result, null, 2));

    console.log('\n📈 Final Orchestrator Status:');
    console.log(JSON.stringify(orchestrator.getOrchestratorStatus(), null, 2));

    console.log('\n🔧 Testing Direct Tool-Based Delegation...');
    
    // Test the new orchestration tools
    const directResult = await orchestrator.run(
      "I need to research the latest trends in AI and then implement a prototype based on the findings",
      {},
      false // Don't use CoT for this example
    );

    console.log('Direct delegation result:', directResult);

  } catch (error) {
    console.error('❌ Error during demonstration:', error.message);
  } finally {
    console.log('\n🛑 Shutting down orchestrator...');
    await orchestrator.shutdown();
    console.log('✅ Shutdown complete!');
  }
}

// Run the demonstration
demonstrateEnhancedAgentFramework().catch(console.error);
