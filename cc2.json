{"agency": {"name": "Parallel Content Creation Agency", "description": "Demonstrates a parallel multi-agent workflow for content creation", "logging": {"level": "basic", "tracing": true}}, "brief": {"content-creation-brief": {"title": "Parallel Content Creation Brief", "overview": "Create multiple content pieces in parallel based on the provided prompt.", "background": "The content creation process can be parallelized to improve efficiency.", "objective": "Produce multiple well-researched, engaging, and polished content pieces simultaneously.", "targetAudience": "General audience interested in music and pop culture."}}, "agents": {"prompt-researcher": {"id": "prompt-researcher", "name": "Prompt Researcher", "description": "Specializes in finding and analyzing relevant information based on a prompt", "role": "You are a research specialist. Your task is to find relevant information based on the provided prompt. Be thorough in your research and provide structured, actionable insights.", "goals": ["Find comprehensive information based on the prompt", "Provide structured research results"], "provider": "gemini", "llmConfig": {"model": "gemini-2.0-flash", "temperature": 0.4, "maxOutputTokens": 3072}, "tools": {"webSearch": "webSearch"}}, "writer-1": {"id": "writer-1", "name": "Writer 1", "description": "Specializes in drafting content based on research", "role": "You are a content writer. Your task is to draft engaging and informative content based on the research provided. Ensure the content is well-structured and aligned with the prompt.", "goals": ["Draft engaging and informative content", "Ensure the content is well-structured and aligned with the prompt"], "provider": "gemini", "llmConfig": {"model": "gemini-2.0-flash", "temperature": 0.5, "maxOutputTokens": 2048}}, "writer-2": {"id": "writer-2", "name": "Writer 2", "description": "Specializes in drafting content based on research", "role": "You are a content writer. Your task is to draft engaging and informative content based on the research provided. Ensure the content is well-structured and aligned with the prompt.", "goals": ["Draft engaging and informative content", "Ensure the content is well-structured and aligned with the prompt"], "provider": "gemini", "llmConfig": {"model": "gemini-2.0-flash", "temperature": 0.5, "maxOutputTokens": 2048}}, "refiner-1": {"id": "refiner-1", "name": "Refiner 1", "description": "Specializes in refining and polishing content", "role": "You are a content refiner. Your task is to refine and polish the draft to improve clarity, coherence, and quality. Ensure the final content is polished and ready for publication.", "goals": ["Refine and polish the draft for clarity and coherence", "Ensure the final content is polished and ready for publication"], "provider": "gemini", "llmConfig": {"model": "gemini-2.0-flash", "temperature": 0.5, "maxOutputTokens": 2048}}, "refiner-2": {"id": "refiner-2", "name": "Refiner 2", "description": "Specializes in refining and polishing content", "role": "You are a content refiner. Your task is to refine and polish the draft to improve clarity, coherence, and quality. Ensure the final content is polished and ready for publication.", "goals": ["Refine and polish the draft for clarity and coherence", "Ensure the final content is polished and ready for publication"], "provider": "gemini", "llmConfig": {"model": "gemini-2.0-flash", "temperature": 0.5, "maxOutputTokens": 2048}}}, "teams": {"content-creation-team": {"name": "Parallel Content Creation Team", "description": "Collaborates to create polished content in parallel based on a prompt", "agents": {"prompt-researcher": "prompt-researcher", "writer-1": "writer-1", "writer-2": "writer-2", "refiner-1": "refiner-1", "refiner-2": "refiner-2"}, "jobs": {"research": {"agent": "prompt-researcher", "description": "Research relevant information based on the prompt", "inputs": {"prompt": "{{initialInputs.prompt}}"}}, "draft-1": {"agent": "writer-1", "description": "Draft content based on the research", "inputs": {"research": "{{research}}"}}, "draft-2": {"agent": "writer-2", "description": "Draft content based on the research", "inputs": {"research": "{{research}}"}}, "refine-1": {"agent": "refiner-1", "description": "Refine and polish the draft", "inputs": {"draft": "{{draft-1}}"}}, "refine-2": {"agent": "refiner-2", "description": "Refine and polish the draft", "inputs": {"draft": "{{draft-2}}"}}}, "workflow": [{"type": "sequential", "jobId": "research", "assigneeId": "prompt-researcher", "assigneeType": "agent"}, {"type": "parallel", "jobId": ["draft-1", "draft-2"], "assigneeId": ["writer-1", "writer-2"], "assigneeType": ["agent", "agent"]}, {"type": "parallel", "jobId": ["refine-1", "refine-2"], "assigneeId": ["refiner-1", "refiner-2"], "assigneeType": ["agent", "agent"]}]}}}